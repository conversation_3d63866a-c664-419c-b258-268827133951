import {cn} from '@/lib/utils';
import {ToastColor, type ToasterProps} from '@/types';
import {SnackbarContent, useSnackbar} from 'notistack';
import {Button} from '../ui';
import {X} from 'lucide-react';
import {TOASTER_STYLES} from '@/constants';
import {TOASTER_ICONS} from './ToasterIcons';
import {useTranslation} from 'react-i18next';

export function Toaster({title, message, id, ref, color = ToastColor.Info, ...other}: Readonly<ToasterProps>) {
  const {t} = useTranslation();
  const current = TOASTER_STYLES[color];
  const Icon = TOASTER_ICONS[color];
  const {closeSnackbar} = useSnackbar();

  const handleClose = () => {
    closeSnackbar(id);
  };

  return (
    <SnackbarContent ref={ref} role="alert" {...other}>
      <div className={cn('rounded-md shadow-lg overflow-hidden min-w-100', current.bg, current.border)}>
        <div className="px-4 py-3  flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon />
            <h3 className={cn('text-sm font-semibold uppercase', current.text)}>{title}</h3>
          </div>

          <Button
            onClick={handleClose}
            className={cn('transition-colors', current.text)}
            variant="ghost"
            size="icon"
            aria-label="Close"
          >
            <X size={18} />
          </Button>
        </div>

        <div className={cn('px-4 py-3 text-sm', current.text)}>{message}</div>

        <div className="px-4 py-2  flex justify-end">
          <Button onClick={handleClose} className={cn('text-xs px-3 py-1 w-13 rounded-sm', current.button)} size="sm">
            {t('OK')}
          </Button>
        </div>
      </div>
    </SnackbarContent>
  );
}
