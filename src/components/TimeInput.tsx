import React, { useState, useRef, useEffect } from 'react';
import { Clock } from 'lucide-react';

type TimePickerProps = {
  defaultValue?: string;
  format?: '12' | '24';
  onChange?: (value: string) => void;
};

const pad = (num: number) => num.toString().padStart(2, '0');

const get24HourValue = (h: number, period?: 'AM' | 'PM') => {
  if (period === 'PM' && h !== 12) return h + 12;
  if (period === 'AM' && h === 12) return 0;
  return h;
};

const TimePicker: React.FC<TimePickerProps> = ({
  defaultValue = '00:00',
  format = '24',
  onChange,
}) => {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState(defaultValue);
  const ref = useRef<HTMLDivElement>(null);

  const date = new Date(`1970-01-01T${value}`);
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const isAM = hours < 12;

  const displayHours = format === '12' ? (hours % 12 || 12) : hours;
  const displayMinutes = pad(minutes);
  const period = isAM ? 'AM' : 'PM';

  const [tempPeriod, setTempPeriod] = useState<'AM' | 'PM'>(period);

  const updateTime = (h: number, m: number, meridian?: 'AM' | 'PM') => {
    const fullHour = format === '12' ? get24HourValue(h, meridian ?? tempPeriod) : h;
    const timeStr = `${pad(fullHour)}:${pad(m)}`;
    setValue(timeStr);
    onChange?.(timeStr);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div ref={ref} className="relative inline-block w-48">
      <div
        onClick={() => setOpen(!open)}
        className="flex items-center justify-between px-3 py-1.5 border border-gray-300 rounded-md bg-white cursor-pointer"
      >
        <span className="text-sm text-black">
          {value ? `${pad(displayHours)}:${displayMinutes}${format === '12' ? ` ${period}` : ''}` : '--:--'}
        </span>
        <Clock className="w-4 h-4 text-gray-600" />
      </div>

      {open && (
        <div className="absolute top-full mt-1 z-10 bg-white border border-gray-300 rounded-md p-2 shadow-lg grid grid-cols-3 gap-2 text-center w-full max-h-48 overflow-hidden">
          <div className="overflow-y-auto max-h-40">
            <div className="font-semibold text-xs mb-1">Hour</div>
            {[...Array(format === '12' ? 12 : 24).keys()].map(i => {
              const h = format === '12' ? i + 1 : i;
              return (
                <div
                  key={h}
                  className="text-sm p-1 rounded hover:bg-gray-200 cursor-pointer"
                  onClick={() => updateTime(h, minutes, format === '12' ? tempPeriod : undefined)}
                >
                  {pad(h)}
                </div>
              );
            })}
          </div>

          <div className="overflow-y-auto max-h-40">
            <div className="font-semibold text-xs mb-1">Min</div>
            {[...Array(60).keys()].filter(m => m % 5 === 0).map(m => (
              <div
                key={m}
                className="text-sm p-1 rounded hover:bg-gray-200 cursor-pointer"
                onClick={() => updateTime(displayHours, m, format === '12' ? tempPeriod : undefined)}
              >
                {pad(m)}
              </div>
            ))}
          </div>

          {format === '12' && (
            <div>
              <div className="font-semibold text-xs mb-1">AM/PM</div>
              {['AM', 'PM'].map(p => (
                <div
                  key={p}
                  className={`text-sm p-1 rounded cursor-pointer hover:bg-gray-200 ${p === tempPeriod ? 'bg-gray-300' : ''}`}
                  onClick={() => {
                    setTempPeriod(p as 'AM' | 'PM');
                    updateTime(displayHours, minutes, p as 'AM' | 'PM');
                  }}
                >
                  {p}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TimePicker;
