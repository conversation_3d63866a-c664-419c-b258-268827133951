import {CommonBreadcrumb} from '@/components/BreadCrumbs/CommonBreadCrumbs';
import {MESSAGES, RouteConstant} from '@/constants';
import {useTranslation} from 'react-i18next';
import {useLocation} from 'react-router-dom';
import {useState} from 'react';
import {Sunrise} from 'lucide-react';
import {Select, SelectContent, SelectItem, SelectValue} from '@/components/ui/select';
import {SelectTrigger} from '@radix-ui/react-select';
import {SlotDuration} from '../components';
import {Input} from '@/components/ui/input';
import {MultiSelect} from '@/components/multi-select';
import {Departments, BussinessHour} from '@/enums';

const RADIO_BUTTON_COMMON_CLASSES = `
  appearance-none w-5 h-5 rounded-full border-2
  checked:border-[var(--primary-accent)] checked:bg-white
  checked:before:content-[''] checked:before:block
  checked:before:w-2.5 checked:before:h-2.5
  checked:before:rounded-full checked:before:bg-[var(--primary-accent)]
  checked:before:mx-auto checked:before:my-auto
  relative before:absolute before:inset-0
`;

const CustomBusinessHoursPanel = () => {
  const [selected, setSelected] = useState<string[]>([]);
  interface LocationState {
    slotName?: string;
  }
  const location = useLocation();
  const state = location.state as LocationState | undefined;
  const slotName = state?.slotName ?? '';
  const isDefaultSlot = slotName === String(BussinessHour.DEFAULT);
  const isAddMode = location.pathname.endsWith('/add');
  const {t} = useTranslation();
  const title = isAddMode ? t(MESSAGES.ADD_CUSTOM_HOURS) : t(MESSAGES.EDIT_CUSTOM_HOURS);
  const [scheduleType, setScheduleType] = useState('sameEveryday');

  return (
    <div className="w-full">
      {/* Breadcrumb */}
      <CommonBreadcrumb
        items={[
          {label: t(MESSAGES.SIDEBAR_MENU_SETTINGS), href: RouteConstant.SETTINGS},
          {label: t(MESSAGES.BUSINESS_HOURS), href: RouteConstant.BUSINESS_HOURS},
          {label: isAddMode ? t(MESSAGES.ADD) : t(MESSAGES.EDIT)},
        ]}
      />
      <div className="min-h-screen pt-7.5 pb-8 flex justify-center items-start px-4 md:px-10">
        <div className="w-full max-w-337.5 bg-white rounded-xl shadow p-6 space-y-6">
          {/* Title */}
          <div className="h-6 text-left text-base leading-6 font-medium text-black tracking-normal">{title}</div>

          {/* Timezone */}
          <div className="flex gap-4 w-full">
            {/* Only show Name input if not default */}
            {!isDefaultSlot && (
              <div>
                <Input
                  className="w-103 h-11 pl-2.5 pr-10 text-sm leading-5 border border-gray-300 rounded-sm focus-visible:border-gray-300 focus-visible:ring-0 flex items-center"
                  placeholder="Name"
                />
              </div>
            )}
            <div className="relative">
              <Select>
                <SelectTrigger className="w-103 h-11 pl-2.5 pr-10 text-sm leading-5 border border-gray-300 rounded-sm focus:outline-none focus:ring-0 focus:border-gray-300 flex items-center">
                  <SelectValue placeholder="(OTC-08:00) Pacific Time" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pst">(OTC-08:00) Pacific Time</SelectItem>
                  <SelectItem value="est">(OTC-05:00) Eastern Time</SelectItem>
                  <SelectItem value="ist">(UTC+05:30) India Standard Time</SelectItem>
                </SelectContent>
              </Select>
              {/* Clock Icon */}
              <span className="absolute right-2.5 top-4 text-gray-400 pointer-events-none">
                <Sunrise className="text-gray-600 w-3.5 h-3.5" />
              </span>
            </div>
          </div>

          {/* Radio buttons */}
          <div className="flex space-x-6">
            {/* Same Everyday */}
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="schedule"
                value="sameEveryday"
                checked={scheduleType === 'sameEveryday'}
                onChange={() => setScheduleType('sameEveryday')}
                className={`${RADIO_BUTTON_COMMON_CLASSES} border-[var(--primary-accent)]`}
              />
              <span className="text-sm leading-5 font-[Poppins] text-[var(-text-label)]">
                {t(MESSAGES.SAME_EVERYDAY)}
              </span>
            </label>

            {/* Custom */}
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="schedule"
                value="custom"
                checked={scheduleType === 'custom'}
                onChange={() => setScheduleType('custom')}
                className={`${RADIO_BUTTON_COMMON_CLASSES} border-[var(--color-vertical-divider)]`}
              />
              <span className="text-sm leading-5 font-[Poppins] text-[var(-text-label)]">{t(MESSAGES.CUSTOM)}</span>
            </label>
          </div>

          {/* Placeholder Text */}
          <p className="text-sm text-gray-400">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
            dolore magna aliqua.
          </p>

          {/* Time Range Input */}
          {scheduleType === 'sameEveryday' ? <SlotDuration /> : <SlotDuration showWeekdays={true} />}

          {/* Add Message */}
          {!isAddMode ? (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">{t(MESSAGES.ADD_MESSAGE)}</label>
              <p className="text-sm text-gray-500 mb-2">{t(MESSAGES.ADD_MESSAGE_DESCRIPTION)}</p>
              <textarea
                className="w-full max-w-124 min-h-20 border border-gray-300 rounded-md px-3 py-2 text-sm resize-none"
                defaultValue="Our agents are unavailable at the moment. Please reach us during our working hours"
              />
            </div>
          ) : (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">{t(MESSAGES.DEPARTMENT)}</label>
              <p className="text-sm text-gray-500 mb-2">{t(MESSAGES.TIME_SLOTS_ASSIGNED_TO_DEPARTMENTS)}</p>
              <div className="relative w-103">
                <label className="text-sm mb-1 block text-muted-foreground">Select Departments</label>
                <MultiSelect
                  options={Object.values(Departments).map(dept => ({ label: dept, value: dept }))}
                  value={selected}
                  onChange={setSelected}
                  placeholder="Select department"
                />
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-4 pt-2">
            {/* Cancel Button */}
            <button
              type="button"
              className="w-26 h-10 bg-white border border-[#D2D2D2] rounded-md text-[var(-text-label)] text-sm font-normal leading-5 tracking-normal cursor-pointer hover:bg-gray-100"
            >
              {t(MESSAGES.CANCEL)}
            </button>

            {/* Add Button */}
            <button
              type="submit"
              className="w-26 h-10 bg-[var(--primary-accent)] rounded-md text-white text-sm font-normal leading-5 tracking-normal cursor-pointer hover:bg-[var(--primary-hover)]]"
            >
              {t(MESSAGES.ADD)}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomBusinessHoursPanel;
