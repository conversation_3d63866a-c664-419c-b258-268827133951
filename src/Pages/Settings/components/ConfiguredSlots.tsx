import {useState} from 'react';
import {Card} from '@/components/ui/card';
import {Pencil, Trash2} from 'lucide-react';
import {useNavigate} from 'react-router-dom';
import {RouteConstant} from '@/constants';

type Slot = {
  id: string;
  title: string;
  time: string;
  department: string;
  editRoute: string;
};

const initialSlots: Slot[] = [
  {
    id: 'default',
    title: 'Default Business Hours',
    time: 'Same everyday: 10:00AM to 06:00PM',
    department: 'All Departments',
    editRoute: RouteConstant.EDIT_BUSINESS_HOURS,
  },
  {
    id: 'custom',
    title: 'Support and Customer',
    time: 'Same everyday: 10:00AM to 06:00PM',
    department: 'Support, Customer',
    editRoute: RouteConstant.EDIT_BUSINESS_HOURS,
  },
];

export const ConfiguredSlots = () => {
  const navigate = useNavigate();
  const [slots, setSlots] = useState(initialSlots);

  const handleDelete = (id: string) => {
    if (id === 'default') return;
    setSlots(prevSlots => prevSlots.filter(slot => slot.id !== id));
  };

  function cn(...classes: (string | false | null | undefined)[]): string {
    return classes.filter(Boolean).join(' ');
  }
  return (
    <>
      {slots.map(slot => (
        <Card key={slot.id} className="p-4 pr-6 relative w-245 h-27.5 border-[var(--color-border-input)] mb-4">
          <div className="absolute top-5 left-5 flex flex-col text-sm leading-5 space-y-1">
            <h4 className="font-medium text-[var(--text-label)]">{slot.title}</h4>
            <p className="font-normal text-gray-600">
              <span className="text-[var(--color-muted-text)]">Timing: </span>
              {slot.time}
            </p>
            <p className="font-normal text-gray-600">
              <span className="text-[var(--color-muted-text)]">Departments: </span>
              {slot.department}
            </p>
          </div>
          <div className={cn('absolute top-5 right-5 flex space-x-3')}>
            <Pencil
              data-testid="pencil-icon"
              className="h-3.5 w-3.5 text-[var(--color-tab-text-default)] cursor-pointer"
              onClick={() => void navigate(slot.editRoute, {state: {slotName: slot.id}})}
            />
            <Trash2
              data-testid="trash2-icon"
              className={cn(
                'h-3.5 w-3.5',
                slot.id === 'default'
                  ? 'text-gray-300 cursor-not-allowed'
                  : 'text-[var(--color-tab-text-default)] cursor-pointer',
              )}
              onClick={() => handleDelete(slot.id)}
              aria-disabled={slot.id === 'default'}
              tabIndex={slot.id === 'default' ? -1 : 0}
            />
          </div>
        </Card>
      ))}
    </>
  );
};
