import {SquarePlus, Trash2} from 'lucide-react';
import TimePicker from '@/components/TimeInput';

interface TimeSlotRowProps {
  slot: {from: string; to: string; id: string};
  slotIndex: number;
  totalSlots: number;
  onAddRow: () => void;
  onRemoveRow: () => void;
  canAdd: boolean;
  canRemove: boolean;
}

export const TimeSlotRow = ({
  slotIndex,
  totalSlots,
  onAddRow,
  onRemoveRow,
  canAdd,
  canRemove,
}: TimeSlotRowProps) => {

  return (
    <div className="flex items-center justify-between pl-2">
      <div className="flex items-center space-x-2">
        {/* From Time */}
        <div className="relative">
          <TimePicker format="12" />
        </div>

        <span className="text-[18px] text-gray-700 px-1">-</span>

        {/* To Time */}
        <div className="relative">
          <TimePicker format="12" />
        </div>
      </div>

      {/* Right icon container */}
      <div className="w-8 h-8.5 flex items-center justify-center">
        {canAdd && slotIndex === totalSlots - 1 ? (
          <SquarePlus
            data-testid="SquarePlusIcon"
            className="w-5 h-5 text-[var(--primary)] cursor-pointer"
            onClick={onAddRow}
          />
        ) : (
          <Trash2
            data-testid="Trash2Icon"
            className={`w-5 h-5 text-gray-500 ${canRemove ? 'cursor-pointer' : 'opacity-50 cursor-not-allowed'}`}
            onClick={() => canRemove && onRemoveRow()}
          />
        )}
      </div>
    </div>
  );
};
