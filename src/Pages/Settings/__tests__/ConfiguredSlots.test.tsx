import {render, screen, fireEvent} from '@testing-library/react';
import {BrowserRouter} from 'react-router-dom';
import {ConfiguredSlots} from '../components/ConfiguredSlots';
import {RouteConstant} from '@/constants';
import {vi, describe, beforeEach, test, expect} from 'vitest';

// Mock the useNavigate hook
const mockedUseNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockedUseNavigate,
  };
});

describe('ConfiguredSlots Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    mockedUseNavigate.mockClear();
  });

  test('navigates to edit business hours page on pencil icon click', () => {
    render(
      <BrowserRouter>
        <ConfiguredSlots />
      </BrowserRouter>,
    );

    const pencilIcon = screen.getAllByTestId('pencil-icon')[0];
    fireEvent.click(pencilIcon);

    expect(mockedUseNavigate).toHaveBeenCalledTimes(1);
    expect(mockedUseNavigate).toHaveBeenCalledWith(RouteConstant.EDIT_BUSINESS_HOURS, {
      state: {slotName: expect.any(String)},
    });
  });
});
