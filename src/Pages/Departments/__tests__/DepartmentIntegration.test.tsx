/* eslint-disable @typescript-eslint/no-explicit-any */
import {describe, it, expect, vi, beforeEach} from 'vitest';
import {render, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {Provider} from 'react-redux';
import {configureStore, type Store} from '@reduxjs/toolkit';
import {SnackbarProvider} from 'notistack';
import DepartmentPage from '../DepartmentPage';
import {apiSlice} from '../../../redux/apiSlice';
import authReducer from '../../../redux/auth/authSlice';

// Mock the API calls
vi.mock('../../../redux/apiSlice', async () => {
  const actual = await vi.importActual<any>('../../../redux/apiSlice');

  return {
    ...actual,
    useCreateDepartmentMutation: () => [
      vi.fn(() =>
        Promise.resolve({data: {id: '3', name: 'Finance', isDefault: false, createdAt: new Date().toISOString()}}),
      ),
      {isLoading: false},
    ],
    useGetDepartmentsQuery: () => ({
      data: mockDepartmentsResponse.data,
      isLoading: false,
    }),
  };
});

// Create a mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      [apiSlice.reducerPath]: apiSlice.reducer,
      auth: authReducer,
    },
    middleware: getDefaultMiddleware => getDefaultMiddleware().concat(apiSlice.middleware),
    preloadedState: initialState,
  });
};

// Mock API responses
const mockDepartmentsResponse = {
  data: [
    {
      id: '1',
      name: 'HR',
      isDefault: false,
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z',
      deletedAt: null,
      agents: [
        {name: 'Olivia Martinez', email: '<EMAIL>'},
        {name: 'Noah Thompson', email: '<EMAIL>'},
      ],
    },
    {
      id: '2',
      name: 'IT',
      isDefault: false,
      createdAt: '2023-01-02T00:00:00Z',
      updatedAt: '2023-01-02T00:00:00Z',
      deletedAt: null,
      agents: [
        {name: 'Olivia Martinez', email: '<EMAIL>'},
        {name: 'Noah Thompson', email: '<EMAIL>'},
      ],
    },
  ],
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1,
  },
};

describe('Department Integration Tests', () => {
  let mockStore: Store;

  beforeEach(() => {
    vi.clearAllMocks();
    mockStore = createMockStore();

    // Mock the API endpoints

    const createMockResponse = (jsonData: any) => {
      const jsonString = JSON.stringify(jsonData);

      const response = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          get: (_key: string) => null,
        },
        json: () => Promise.resolve(jsonData),
        text: () => Promise.resolve(jsonString),
      };

      return {
        ...response,
        clone: () => ({
          ...response,
        }),
      };
    };

    globalThis.fetch = vi.fn().mockImplementation((input: RequestInfo, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.url;

      if (url.includes('/departments') && init?.method !== 'POST') {
        return Promise.resolve(createMockResponse(mockDepartmentsResponse));
      } else if (url.includes('/departments') && init?.method === 'POST') {
        return Promise.resolve(
          createMockResponse({
            id: '3',
            name: 'Finance',
            isDefault: false,
            createdAt: new Date().toISOString(),
          }),
        );
      }

      return Promise.reject(new Error('Not found'));
    });
  });

  it('should load and display departments', async () => {
    render(
      <Provider store={mockStore}>
        <SnackbarProvider>
          <DepartmentPage />
        </SnackbarProvider>
      </Provider>,
    );

    // Wait for departments to load
    await waitFor(() => {
      expect(screen.getByText('HR')).toBeInTheDocument();
      expect(screen.getByText('IT')).toBeInTheDocument();
    });
  });

  it('should create a new department', async () => {
    const user = userEvent.setup();

    render(
      <Provider store={mockStore}>
        <SnackbarProvider>
          <DepartmentPage />
        </SnackbarProvider>
      </Provider>,
    );

    // Open the add department modal
    const addButton = screen.getByTestId('add-button');
    await user.click(addButton);

    // Fill in the department name
    const nameInput = screen.getByTestId('Name');
    await user.type(nameInput, 'Finance');

    // Submit the form
    const saveButton = screen.getByText('ADD');
    await user.click(saveButton);

    // Check if the API was called with the correct data
    // Check if the API was called with the correct data
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled();
    });

    // Check if success message is shown
    // await waitFor(() => {
    //   expect(screen.getByText('Department created successfully')).toBeInTheDocument();
    // });
  });

  it('should handle API errors when creating a department', async () => {
    const user = userEvent.setup();

    // Mock a failed API call
    global.fetch = vi.fn().mockImplementation(() => {
      return Promise.reject(new Error('API Error'));
    });

    render(
      <Provider store={mockStore}>
        <SnackbarProvider>
          <DepartmentPage />
        </SnackbarProvider>
      </Provider>,
    );

    // Open the add department modal
    const addButton = screen.getByTestId('add-button');
    await user.click(addButton);

    // Fill in the department name
    const nameInput = screen.getByTestId('Name');
    await user.type(nameInput, 'Finance');

    // Submit the form
    const saveButton = screen.getByText('ADD');
    await user.click(saveButton);

    // Check if error message is shown
    // await waitFor(() => {
    //   expect(screen.getByText('Failed to create department')).toBeInTheDocument();
    // });
  });

  it('should validate department name', async () => {
    const user = userEvent.setup();

    render(
      <Provider store={mockStore}>
        <SnackbarProvider>
          <DepartmentPage />
        </SnackbarProvider>
      </Provider>,
    );

    // Open the add department modal
    const addButton = screen.getByTestId('add-button');
    await user.click(addButton);

    // Check that submit button is disabled when form is empty
    const saveButton = screen.getByText('ADD');
    expect(saveButton).toBeDisabled();

    // Enter an invalid name with special characters
    const nameInput = screen.getByTestId('Name');
    await user.type(nameInput, 'HR@Department!');

    // Check that submit button is still disabled for invalid input
    expect(saveButton).toBeDisabled();

    // Clear the input and enter spaces only
    await user.clear(nameInput);
    await user.type(nameInput, '   ');

    // Check that submit button is still disabled for spaces-only input
    expect(saveButton).toBeDisabled();

    // API should not be called for invalid submissions
    expect(global.fetch).not.toHaveBeenCalledWith(
      expect.stringContaining('/departments'),
      expect.objectContaining({method: 'POST'}),
    );
  });
});
