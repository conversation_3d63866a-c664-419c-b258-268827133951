import {DataTable} from '../../components/data-table';
import {
  useCreateDepartmentMutation,
  useGetDepartmentsQuery,
  type DepartmentQueryOptions,
} from '../../redux/departments/departmentSlice';
import {departmentColumns} from './DepartmentColumns';
import {useState, useMemo} from 'react';
import type {SortItem} from '../../types/filter.types';
import {FilterOperator} from '../../types/filter.types';
import type {DepartmentFormValues} from '../../validations/departmentSchema';
import {DepartmentForm} from './DepartmentForm';
import SuccessModal from '../../components/modals/SuccessModal';
import {MESSAGES} from '../../constants';
import {useTranslation} from 'react-i18next';
import {useDebounce, useToast} from '@/hooks';
import {ToastColor} from '@/types';
import type {ApiError, ApiResponse} from '@/types/api.type';
import type {UseFormReset} from 'react-hook-form';

const DEBOUNCE_DELAY = 300;

const DepartmentPage = () => {
  const {t} = useTranslation();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [successModalOpen, setSuccessModalOpen] = useState<boolean>(false);

  const [createDepartment, {isLoading: isCreating}] = useCreateDepartmentMutation();

  const debouncedQuery = useDebounce(searchQuery, DEBOUNCE_DELAY);
  const {toast} = useToast();

  // Create filter options based on search query
  const filterOptions = useMemo<DepartmentQueryOptions>(() => {
    const order = [['createdAt', 'DESC']] as SortItem[];

    if (!debouncedQuery)
      return {
        order,
        includeAgents: true,
      };
    return {
      where: {
        name: {
          [FilterOperator.LIKE]: `%${debouncedQuery}%`,
        },
      },
      order,
      includeAgents: true,
    };
  }, [debouncedQuery]);

  const {data: departments, refetch, isLoading: isFetchingDepartments} = useGetDepartmentsQuery(filterOptions);

  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  const handleAddDepartment = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitDepartment = async (
    values: DepartmentFormValues,
    reset: () => UseFormReset<{
      name: string;
    }>,
  ) => {
    try {
      await createDepartment({name: values.name}).unwrap();
      setSuccessModalOpen(true);
      void refetch();
      reset();
      handleCloseModal();
    } catch (error) {
      const message = ((error as ApiResponse<unknown>).data as ApiError)?.message;
      toast({
        title: 'Error',
        description: message ?? t('DEPARTMENT_CREATION_FAILED'),
        color: ToastColor.Error,
      });
    }
  };

  return (
    <div className="w-full">
      <DataTable
        columns={departmentColumns(t)}
        data={departments?.data ?? []}
        buttonLabel={t('ADD_DEPARTMENT')}
        searchPlaceholder={t('SEARCH')}
        onSearch={handleSearch}
        onButtonClick={handleAddDepartment}
        isLoading={isFetchingDepartments}
      />
      <DepartmentForm
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={(values, reset) => void handleSubmitDepartment(values, reset)}
        isLoading={isCreating}
      />
      <SuccessModal
        isOpen={successModalOpen}
        onClose={() => {
          setSuccessModalOpen(false);
        }}
        message={t(MESSAGES.DEPARTMENT_CREATED)}
      />
    </div>
  );
};

export default DepartmentPage;
