import {apiSlice} from '../apiSlice';
import type {DepartmentListResponse, IDepartment} from '../../types';
import type {FilterOptions} from '../../types/filter.types';
import {ApiSliceIdentifier} from '@/constants';

export interface DepartmentQueryOptions extends FilterOptions {
  includeAgents?: boolean;
}

const apiSliceIdentifier = ApiSliceIdentifier.AGENT_PORTAL_FACADE;

const buildDepartmentFilter = (options?: DepartmentQueryOptions | void): Record<string, string> | undefined => {
  if (!options) return undefined;

  const {includeAgents, ...filterOptions} = options;
  const params: Record<string, string> = {};

  // Add filter params if any filter options exist
  if (Object.keys(filterOptions).length > 0) {
    params.filter = JSON.stringify(filterOptions);
  }

  // Add includeAgents param if specified
  if (includeAgents !== undefined) {
    params.includeAgents = includeAgents.toString();
  }

  return Object.keys(params).length > 0 ? params : undefined;
};

export const departmentApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getDepartments: builder.query<DepartmentListResponse, DepartmentQueryOptions | void>({
      query: queryOptions => ({
        url: '/departments',
        apiSliceIdentifier,
        params: buildDepartmentFilter(queryOptions),
      }),
    }),

    getDepartmentById: builder.query<IDepartment, {id: string | number; options?: FilterOptions}>({
      query: ({id, options}) => ({
        url: `/departments/${id}`,
        apiSliceIdentifier,
        params: JSON.stringify(options),
      }),
    }),

    createDepartment: builder.mutation<IDepartment, {name: string}>({
      query: department => ({
        url: '/departments',
        method: 'POST',
        body: department,
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {useGetDepartmentsQuery, useGetDepartmentByIdQuery, useCreateDepartmentMutation} = departmentApiSlice;
