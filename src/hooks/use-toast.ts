import {ToastColor} from '@/types';
import {useSnackbar, type OptionsObject} from 'notistack';
import {TOAST_AUTO_HIDE_DURATION} from '@/constants';

interface ToastOptions {
  title?: string;
  description: string;
  duration?: number;
  color?: ToastColor;
  anchorOrigin?: OptionsObject['anchorOrigin'];
}
export interface CustomSnackbarOptions extends OptionsObject {
  title?: string;
  color?: ToastColor;
}

export function useToast() {
  const {enqueueSnackbar} = useSnackbar();

  const toast = ({
    title,
    description,
    color = ToastColor.Info,
    duration = TOAST_AUTO_HIDE_DURATION,
    anchorOrigin = {vertical: 'top', horizontal: 'right'},
  }: ToastOptions) => {
    const options: CustomSnackbarOptions = {
      variant: 'custom',
      autoHideDuration: duration,
      title,
      color,
      anchorOrigin,
    };

    enqueueSnackbar(description, options);
  };

  return {toast};
}
