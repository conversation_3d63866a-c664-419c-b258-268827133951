import * as z from 'zod';

export const departmentSchema = z.object({
  name: z
    .string()
    .transform(val => val.trim()) // Trim first
    .refine(val => val.length > 0, 'Department name is required') // Check after trimming
    .refine(val => !/^\s*$/.test(val), 'Department name cannot be only spaces') // Prevent spaces-only
    .refine(val => /^[a-zA-Z0-9\s]+$/.test(val), 'Only alphanumeric characters are allowed'), // Use refine for regex
});

export type DepartmentFormValues = z.infer<typeof departmentSchema>;
