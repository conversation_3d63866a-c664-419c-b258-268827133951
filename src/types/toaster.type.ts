import type {SnackbarContentProps} from 'notistack';
import type {JSX} from 'react';

export enum ToastColor {
  Success = 'success',
  Error = 'error',
  Warning = 'warning',
  Info = 'info',
  Destructive = 'destructive',
}

export type ToasterVariant = keyof typeof ToastColor;

export interface ToasterProps extends SnackbarContentProps {
  title?: string;
  message?: string;
  onClose?: () => void;
  ref?: React.Ref<HTMLDivElement>;
  color?: ToastColor;
  id?: string | number;
}

export interface ToasterStyle {
  bg: string;
  text: string;
  border: string;
  button: string;
}

export type ToasterIcon = Record<ToastColor, () => JSX.Element>;

export type ToasterStyleMap = Record<ToastColor, ToasterStyle>;
